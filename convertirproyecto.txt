@echo off
setlocal

echo =================================================================
echo  CONVIRTIENDO PROYECTO PARA ACTIVACION CON MAC ADDRESS (ELECTRON)
echo =================================================================
echo.
echo Este script modificara App.tsx para usar el MAC address real
echo en lugar del ID del navegador. Esto es necesario para la version .exe.
echo.
echo Se creara una copia de seguridad como App.tsx.bak la primera vez.
echo.

set "file=App.tsx"

if not exist "%file%" (
    echo [ERROR] No se encontro el archivo %file%.
    pause
    exit /b
)

REM Crear copia de seguridad si no existe
if not exist "%file%.bak" (
    copy "%file%" "%file%.bak" > nul
    echo Copia de seguridad creada: %file%.bak
)

echo Modificando %file%...

powershell -ExecutionPolicy Bypass -Command "                                                                           `
    $filePath = '%file%';                                                                                               `
    $content = Get-Content $filePath -Raw;                                                                              `
                                                                                                                        `
    $electronBlockCommented = '(?s)/\*(.*?// @ts-ignore \(to access the API exposed on the window object\).*?)\*/';      `
    $webBlockActive = '(?s)(// --- Web Browser / Default Implementation.*?setIsCheckingActivation\(false\);)';            `
                                                                                                                        `
    if ($content -match $electronBlockCommented) {                                                                      `
        Write-Host 'Modificando para usar MAC Address (Electron)...' -ForegroundColor Cyan;                               `
        $content = $content -replace $electronBlockCommented, '$1';                                                     `
        $content = $content -replace $webBlockActive, '/*`r`n$1`r`n        */';                                         `
        $content | Set-Content -Path $filePath;                                                                         `
        Write-Host 'Modificacion completada.' -ForegroundColor Green;                                                    `
    } else {                                                                                                            `
        Write-Host 'El archivo ya parece estar modificado para MAC Address. No se realizaron cambios.' -ForegroundColor Yellow; `
    }                                                                                                                   `
"

echo.
echo Hecho. El archivo %file% ha sido modificado.
echo Para revertir, restaura la copia de seguridad %file%.bak.
echo.
pause
