@echo off
setlocal

echo =================================================================
echo  CONSTRUYENDO EJECUTABLE .EXE PARA WINDOWS
echo =================================================================
echo.
echo Este script empaqueta la aplicacion en un archivo .exe usando
echo Electron y electron-builder.
echo.
echo --- INSTRUCCIONES DE CONFIGURACION (realizar una sola vez) ---
echo.
echo 1. INSTALAR NODE.JS: Si no lo tienes, descargalo desde https://nodejs.org/
echo.
echo 2. CREAR package.json: Crea un archivo llamado 'package.json' en la
echo    raiz del proyecto con el siguiente contenido:
echo.
echo    {
echo      "name": "strategy-creator-electron",
echo      "version": "2.4.0",
echo      "description": "Desktop version of the Strategy Creator",
echo      "main": "main.js",
echo      "scripts": {
echo        "start": "electron .",
echo        "dist": "electron-builder"
echo      },
echo      "devDependencies": {
echo        "electron": "^28.0.0",
echo        "electron-builder": "^24.9.1"
echo      },
echo      "build": {
echo        "appId": "com.xdepredadorxd.strategycreator",
echo        "productName": "Strategy Creator 2.4",
echo        "files": [ "**/*", "!*.bak", "!*.bat", "!*.txt", "!*node_modules*" ],
echo        "win": { "target": "nsis" }
echo      }
echo    }
echo.
echo 3. CREAR main.js: Este es el proceso principal de Electron. Crea
echo    un archivo 'main.js' con el siguiente contenido:
echo.
echo    const { app, BrowserWindow, ipcMain } = require('electron');
echo    const path = require('path');
echo    const { networkInterfaces } = require('os');
echo    
echo    const macAddress = Object.values(networkInterfaces())
echo        .flat().find(net => net && !net.internal && net.mac)?.mac;
echo    
echo    function createWindow() {
echo      const win = new BrowserWindow({
echo        width: 1280, height: 720,
echo        webPreferences: { preload: path.join(__dirname, 'preload.js') }
echo      });
echo      win.loadFile('index.html');
echo    }
echo    
echo    app.whenReady().then(() => {
echo      ipcMain.handle('get-mac-address', () => macAddress);
echo      createWindow();
echo    });
echo.
echo 4. CREAR preload.js: Este script conecta Electron con tu app. Crea
echo    un archivo 'preload.js' con el siguiente contenido:
echo.
echo    const { contextBridge, ipcRenderer } = require('electron');
echo    
echo    contextBridge.exposeInMainWorld('electronAPI', {
echo      getMacAddress: () => ipcRenderer.invoke('get-mac-address')
echo    });
echo.
echo 5. EJECUTAR 'convertirproyecto.bat' para activar el codigo de MAC Address.
echo.
echo --- FIN DE INSTRUCCIONES DE CONFIGURACION ---
echo.
echo Una vez configurado, este script instalara las dependencias y construira el .exe.
echo.
pause

REM El script continua para ejecutar los comandos...
if not exist "package.json" (
    echo [ERROR] No se encuentra 'package.json'. Por favor, sigue las instrucciones de arriba.
    pause
    goto :eof
)

echo.
echo Paso 1: Instalando dependencias (electron, electron-builder)...
npm install

if %errorlevel% neq 0 (
    echo [ERROR] Fallo la instalacion de dependencias con npm.
    echo Asegurate de que Node.js y npm esten instalados y funcionen correctamente.
    pause
    goto :eof
)

echo.
echo Paso 2: Empaquetando la aplicacion... El proceso puede tardar varios minutos.
npm run dist

if %errorlevel% neq 0 (
    echo [ERROR] Fallo el empaquetado con electron-builder.
    echo Revisa los mensajes de error de arriba.
    pause
    goto :eof
)

echo.
echo ==========================================================
echo  ¡EMPAQUETADO COMPLETADO!
echo ==========================================================
echo.
echo El instalador (.exe) se encuentra en la carpeta 'dist'.
echo.
pause
:eof
