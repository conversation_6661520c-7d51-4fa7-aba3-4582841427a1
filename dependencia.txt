@echo off
echo ==========================================================
echo  Iniciando servidor de desarrollo local...
echo ==========================================================
echo.
echo  Este script iniciara un servidor web simple para probar
echo  el proyecto en tu navegador.
echo.
echo  Asegurate de tener Python instalado y en el PATH.
echo  Si no lo tienes, puedes usar otras herramientas como
echo  la extension "Live Server" de VS Code.
echo.

REM Check for Python
python --version >nul 2>nul
if %errorlevel% neq 0 (
    echo [ADVERTENCIA] Python no encontrado. No se puede iniciar el servidor.
    echo Por favor, instala Python (https://www.python.org/downloads/)
    echo o usa otra herramienta para servir archivos estaticos.
    pause
    exit /b
)

echo Python encontrado. Iniciando servidor en el puerto 8000...
echo.
echo Abre tu navegador y ve a: http://localhost:8000
echo.
echo Presiona Ctrl+C en esta ventana para detener el servidor.
echo.

python -m http.server 8000
